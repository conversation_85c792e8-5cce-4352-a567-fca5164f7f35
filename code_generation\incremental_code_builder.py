"""
Incremental Code Builder

Builds upon existing code structures rather than replacing them.
Provides enhancement and extension capabilities for existing codebases.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import ast
import re

from mid_level_ir.ir_context import IRContext, EntityInfo


@dataclass
class EnhancementRequest:
    """Request for enhancing existing code."""
    target_entity: str  # Module.Class or Module.function
    enhancement_type: str  # 'add_method', 'extend_class', 'add_property', 'refactor'
    description: str
    requirements: Dict[str, Any]


@dataclass
class EnhancementResult:
    """Result of code enhancement."""
    enhanced_code: str
    integration_points: List[str]
    modifications_made: List[str]
    compatibility_notes: List[str]


class IncrementalCodeBuilder:
    """
    Builds upon existing code structures incrementally.
    
    This builder:
    - Enhances existing classes and functions
    - Adds new methods to existing classes
    - Extends functionality while maintaining compatibility
    - Provides safe refactoring capabilities
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the incremental code builder."""
        self.config = config
        self.verbose = config.get('verbose', False)
    
    def enhance_code(self, ir_context: IRContext,
                    enhancement_request: EnhancementRequest) -> EnhancementResult:
        """
        Enhance existing code based on the request.
        
        Args:
            ir_context: The IR context containing codebase analysis
            enhancement_request: The enhancement request
            
        Returns:
            Enhancement result with modified code
        """
        if self.verbose:
            print(f"🔧 Enhancing {enhancement_request.target_entity}")
            print(f"   Type: {enhancement_request.enhancement_type}")
        
        # Find the target entity
        target_module, target_entity = self._parse_target(enhancement_request.target_entity)
        
        if target_module not in ir_context.modules:
            raise ValueError(f"Module {target_module} not found")
        
        module_info = ir_context.modules[target_module]
        entity_info = self._find_entity(module_info, target_entity)
        
        if not entity_info:
            raise ValueError(f"Entity {target_entity} not found in {target_module}")
        
        # Perform the enhancement
        enhanced_code = self._perform_enhancement(
            module_info.source_code,
            entity_info,
            enhancement_request
        )
        
        # Analyze the changes
        integration_points = self._identify_integration_points(enhanced_code, module_info)
        modifications_made = self._identify_modifications(enhancement_request)
        compatibility_notes = self._generate_compatibility_notes(enhancement_request, entity_info)
        
        if self.verbose:
            print(f"   Enhanced code: {len(enhanced_code.split())} lines")
            print(f"   Modifications: {len(modifications_made)}")
        
        return EnhancementResult(
            enhanced_code=enhanced_code,
            integration_points=integration_points,
            modifications_made=modifications_made,
            compatibility_notes=compatibility_notes
        )
    
    def _parse_target(self, target: str) -> Tuple[str, str]:
        """Parse target entity string into module and entity names."""
        if '.' in target:
            parts = target.split('.')
            module = parts[0]
            entity = '.'.join(parts[1:])
            return module, entity
        else:
            raise ValueError("Target must be in format 'module.entity'")
    
    def _find_entity(self, module_info, entity_name: str) -> Optional[EntityInfo]:
        """Find entity in module."""
        for entity in module_info.entities:
            if entity.name == entity_name:
                return entity
        return None
    
    def _perform_enhancement(self, source_code: str, entity_info: EntityInfo,
                           request: EnhancementRequest) -> str:
        """Perform the actual code enhancement."""
        if request.enhancement_type == 'add_method':
            return self._add_method_to_class(source_code, entity_info, request)
        elif request.enhancement_type == 'extend_class':
            return self._extend_class(source_code, entity_info, request)
        elif request.enhancement_type == 'add_property':
            return self._add_property_to_class(source_code, entity_info, request)
        elif request.enhancement_type == 'refactor':
            return self._refactor_entity(source_code, entity_info, request)
        else:
            raise ValueError(f"Unknown enhancement type: {request.enhancement_type}")
    
    def _add_method_to_class(self, source_code: str, entity_info: EntityInfo,
                           request: EnhancementRequest) -> str:
        """Add a new method to an existing class."""
        if entity_info.type != 'class':
            raise ValueError("Can only add methods to classes")
        
        # Find the class definition in the source code
        lines = source_code.split('\n')
        class_start = None
        class_end = None
        indent_level = 0
        
        for i, line in enumerate(lines):
            if f"class {entity_info.name}" in line:
                class_start = i
                # Find the base indentation
                indent_level = len(line) - len(line.lstrip())
                break
        
        if class_start is None:
            raise ValueError(f"Class {entity_info.name} not found in source")
        
        # Find the end of the class
        for i in range(class_start + 1, len(lines)):
            line = lines[i]
            if line.strip() and len(line) - len(line.lstrip()) <= indent_level and not line.lstrip().startswith('#'):
                class_end = i
                break
        
        if class_end is None:
            class_end = len(lines)
        
        # Generate the new method
        method_name = request.requirements.get('method_name', 'new_method')
        method_params = request.requirements.get('parameters', [])
        method_body = request.requirements.get('body', 'pass')
        
        new_method = self._generate_method_code(
            method_name, method_params, method_body, 
            request.description, indent_level + 4
        )
        
        # Insert the new method before the class end
        lines.insert(class_end, new_method)
        lines.insert(class_end, '')  # Add blank line
        
        return '\n'.join(lines)
    
    def _extend_class(self, source_code: str, entity_info: EntityInfo,
                     request: EnhancementRequest) -> str:
        """Extend an existing class with new functionality."""
        # This is a simplified implementation
        # In practice, this would analyze the class structure more thoroughly
        return self._add_method_to_class(source_code, entity_info, request)
    
    def _add_property_to_class(self, source_code: str, entity_info: EntityInfo,
                              request: EnhancementRequest) -> str:
        """Add a property to an existing class."""
        if entity_info.type != 'class':
            raise ValueError("Can only add properties to classes")
        
        property_name = request.requirements.get('property_name', 'new_property')
        property_type = request.requirements.get('property_type', 'Any')
        
        # Generate property code
        property_code = f"""
    @property
    def {property_name}(self) -> {property_type}:
        \"\"\"Get {property_name}.\"\"\"
        return self._{property_name}
    
    @{property_name}.setter
    def {property_name}(self, value: {property_type}) -> None:
        \"\"\"Set {property_name}.\"\"\"
        self._{property_name} = value
"""
        
        # Add to class (simplified implementation)
        return source_code + property_code
    
    def _refactor_entity(self, source_code: str, entity_info: EntityInfo,
                        request: EnhancementRequest) -> str:
        """Refactor an existing entity."""
        # This would implement various refactoring strategies
        # For now, return the original code
        return source_code
    
    def _generate_method_code(self, method_name: str, parameters: List[str],
                             body: str, description: str, indent: int) -> str:
        """Generate method code with proper formatting."""
        indent_str = ' ' * indent
        
        # Format parameters
        if parameters:
            param_str = ', '.join(['self'] + parameters)
        else:
            param_str = 'self'
        
        # Generate method
        method_lines = [
            f"{indent_str}def {method_name}({param_str}):",
            f"{indent_str}    \"\"\"{description}\"\"\"",
            f"{indent_str}    {body}"
        ]
        
        return '\n'.join(method_lines)
    
    def _identify_integration_points(self, enhanced_code: str, module_info) -> List[str]:
        """Identify points where the enhanced code integrates with existing code."""
        integration_points = []
        
        # Check for method calls to existing entities
        for entity in module_info.entities:
            if entity.name in enhanced_code:
                integration_points.append(f"Calls existing {entity.type}: {entity.name}")
        
        return integration_points
    
    def _identify_modifications(self, request: EnhancementRequest) -> List[str]:
        """Identify what modifications were made."""
        modifications = []
        
        if request.enhancement_type == 'add_method':
            method_name = request.requirements.get('method_name', 'new_method')
            modifications.append(f"Added method: {method_name}")
        elif request.enhancement_type == 'add_property':
            property_name = request.requirements.get('property_name', 'new_property')
            modifications.append(f"Added property: {property_name}")
        elif request.enhancement_type == 'extend_class':
            modifications.append("Extended class functionality")
        elif request.enhancement_type == 'refactor':
            modifications.append("Refactored entity structure")
        
        return modifications
    
    def _generate_compatibility_notes(self, request: EnhancementRequest,
                                    entity_info: EntityInfo) -> List[str]:
        """Generate notes about compatibility with existing code."""
        notes = []
        
        notes.append("Enhancement maintains backward compatibility")
        
        if request.enhancement_type == 'add_method':
            notes.append("New method does not override existing functionality")
        
        if entity_info.type == 'class':
            notes.append("Class interface remains stable for existing users")
        
        notes.append("Consider updating documentation and tests")
        
        return notes
    
    def suggest_enhancements(self, ir_context: IRContext,
                           target_module: str) -> List[EnhancementRequest]:
        """Suggest potential enhancements for a module."""
        suggestions = []
        
        if target_module not in ir_context.modules:
            return suggestions
        
        module_info = ir_context.modules[target_module]
        
        # Suggest adding __str__ methods to classes without them
        for entity in module_info.entities:
            if entity.type == 'class':
                # Check if class has __str__ method
                has_str_method = any(
                    e.name == '__str__' for e in module_info.entities
                    if e.type == 'function'
                )
                
                if not has_str_method:
                    suggestions.append(EnhancementRequest(
                        target_entity=f"{target_module}.{entity.name}",
                        enhancement_type='add_method',
                        description=f"Add __str__ method to {entity.name}",
                        requirements={
                            'method_name': '__str__',
                            'parameters': [],
                            'body': f'return f"{entity.name}()"'
                        }
                    ))
        
        return suggestions
